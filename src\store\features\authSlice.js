'use client';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '@/lib/api';
import { setToken, setUser, loadAuthFromCookies, clearAllAuthData } from '@/lib/auth';

// Thunk for handling logout with server-side token invalidation
export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { dispatch }) => {
    try {
      // Try to call logout API to invalidate token on server
      await api.post('/logout');
    } catch (error) {
      // Even if server logout fails, we still want to clear local data
      console.warn('Server logout failed, but continuing with local logout:', error);
    }

    // Always clear all authentication data locally
    clearAllAuthData();

    // Clear cart data as well
    const { clearCart } = await import('./cartSlice');
    dispatch(clearCart());

    // Redirect to login page
    if (typeof window !== 'undefined') {
      // Small delay to ensure state is updated before redirect
      setTimeout(() => {
        window.location.href = '/login';
      }, 100);
    }

    return true;
  }
);

// Initialize state from cookies if available
const initialState = loadAuthFromCookies();

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isLoading = false;
      state.isAuth = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;

      // Save to cookies using utility functions
      setToken(action.payload.token);
      setUser(action.payload.user);
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    // Synchronous logout for immediate local cleanup (used by API interceptor)
    logout: (state) => {
      // Reset auth state
      state.user = null;
      state.token = null;
      state.error = null;
      state.isAuth = false;

      // Clear all authentication data
      clearAllAuthData();

      // Note: Cart clearing is handled by the API interceptor separately
      // to avoid circular dependency issues
    },
    // Add a new action to initialize auth state from cookies
    initFromCookies: (state) => {
      const authData = loadAuthFromCookies();
      state.isAuth = authData.isAuth;
      state.token = authData.token;
      state.user = authData.user;
      state.isLoading = authData.isLoading;
      state.error = authData.error;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        // Reset auth state
        state.user = null;
        state.token = null;
        state.error = null;
        state.isAuth = false;
        state.isLoading = false;
      })
      .addCase(logoutUser.rejected, (state) => {
        // Even if logout fails, clear local state
        state.user = null;
        state.token = null;
        state.error = null;
        state.isAuth = false;
        state.isLoading = false;
      });
  },
});

export const { loginStart, loginSuccess, loginFailure, logout, initFromCookies } = authSlice.actions;
export default authSlice.reducer;

// Thunk for handling login
export const loginUser = (credentials) => async (dispatch) => {
  try {
    dispatch(loginStart());
    const response = await api.post('/login', credentials);
    
    dispatch(
      loginSuccess({
        user: response.user,
        token: response.token,
      })
    );
    return response;
  } catch (error) {
    dispatch(loginFailure(error?.response?.data?.message || 'Login failed'));
    throw error;
  }
};
