import axios from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'sonner';
import { API_URL } from '@/config';
import { clearAllAuthData } from './auth';

// Base Axios instance
const api = axios.create({
  baseURL: API_URL,
  // timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request Interceptor
api.interceptors.request.use(
  (config) => {
    // Get the token from cookies
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    toast.error('Request failed. Please try again.');
    return Promise.reject(error);
  }
);

// Response Interceptor for Success and Error Handling
api.interceptors.response.use(
  (response) => {
    if (response?.config?.showSuccessMessage) {
      toast.success(response.config.successMessage || 'Operation completed successfully!');
    }
    return response.data; // Simplify response handling
  },
  (error) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Clear all authentication data
      clearAllAuthData();

      // Clear cart data from localStorage (since it's persisted)
      if (typeof window !== 'undefined') {
        try {
          // Clear the persisted cart data
          localStorage.removeItem('persist:cart');
        } catch (e) {
          console.warn('Failed to clear cart data:', e);
        }
      }

      // Show appropriate message
      toast.error('Your session has expired. Please login again.');

      // Redirect to login page
      if (typeof window !== 'undefined') {
        // Store current path for redirect after login
        const currentPath = window.location.pathname;
        if (currentPath !== '/login' && currentPath !== '/register') {
          localStorage.setItem('returnTo', currentPath);
        }

        // Redirect to login
        window.location.href = '/login';
      }

      return Promise.reject(error);
    }

    // Handle other errors
    const errorMessage = error.response?.data?.message || 'Something went wrong!';
    toast.error(errorMessage);
    return Promise.reject(error);
  }
);

export default api;
